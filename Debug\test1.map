******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Sat Aug  2 13:26:25 2025

OUTPUT FILE NAME:   <test1.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00002a55


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00004ca8  0001b358  R  X
  SRAM                  20200000   00008000  00000923  000076dd  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00004ca8   00004ca8    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00003140   00003140    r-x .text
  00003200    00003200    00001a50   00001a50    r-- .rodata
  00004c50    00004c50    00000058   00000058    r-- .cinit
20200000    20200000    0000072a   00000000    rw-
  20200000    20200000    00000561   00000000    rw- .bss
  20200568    20200568    000001c2   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00003140     
                  000000c0    000005d2     Ganway.o (.text.Way)
                  00000692    00000002     libclang_rt.builtins.a : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  00000694    000001d0     oled.o (.text.OLED_ShowChar)
                  00000864    00000194     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  000009f8    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  00000b8a    00000002     --HOLE-- [fill = 0]
                  00000b8c    00000188     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_MCU_Ganv_Sensor_Init)
                  00000d14    00000120     encoder.o (.text.GROUP1_IRQHandler)
                  00000e34    00000118     empty.o (.text.main)
                  00000f4c    0000010c     motor.o (.text.Set_PWM)
                  00001058    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  00001164    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  00001268    000000e8                 : dl_timer.o (.text.DL_Timer_initTimerMode)
                  00001350    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  00001434    000000e2     oled.o (.text.OLED_ShowNum)
                  00001516    000000de     oled.o (.text.OLED_Init)
                  000015f4    000000dc     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  000016d0    000000d8     empty.o (.text.TIMG0_IRQHandler)
                  000017a8    000000d0     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Analog_value)
                  00001878    000000aa     No_Mcu_Ganv_Grayscale_Sensor.o (.text.normalizeAnalogValues)
                  00001922    0000009a     oled.o (.text.OLED_ShowSignedNum)
                  000019bc    0000009a     oled.o (.text.OLED_ShowString)
                  00001a56    00000002     --HOLE-- [fill = 0]
                  00001a58    00000090     oled.o (.text.OLED_DrawPoint)
                  00001ae8    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_0_init)
                  00001b74    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00001c00    00000084     oled.o (.text.OLED_Refresh)
                  00001c84    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  00001d08    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00001d84    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  00001df8    00000072     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_MCU_Ganv_Sensor_Init_Frist)
                  00001e6a    00000002     --HOLE-- [fill = 0]
                  00001e6c    0000006c     oled.o (.text.OLED_WR_Byte)
                  00001ed8    0000006c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.convertAnalogToDigital)
                  00001f44    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  00001fac    00000062                            : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  0000200e    00000002     --HOLE-- [fill = 0]
                  00002010    00000060     oled.o (.text.OLED_Clear)
                  00002070    0000005e     driverlib.a : dl_i2c.o (.text.DL_I2C_fillControllerTXFIFO)
                  000020ce    00000002     --HOLE-- [fill = 0]
                  000020d0    00000058     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  00002128    00000054     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_0_init)
                  0000217c    00000050     oled.o (.text.DL_I2C_startControllerTransfer)
                  000021cc    00000050     ti_msp_dl_config.o (.text.SysTick_Config)
                  0000221c    0000004c     empty.o (.text.DL_ADC12_initSingleSample)
                  00002268    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  000022b4    0000004a     ti_msp_dl_config.o (.text.DL_ADC12_configConversionMem)
                  000022fe    00000002     --HOLE-- [fill = 0]
                  00002300    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  0000234a    0000004a     No_Mcu_Ganv_Grayscale_Sensor.o (.text.adc_getValue)
                  00002394    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  000023dc    00000048     oled.o (.text.OLED_DisplayTurn)
                  00002424    00000048     ti_msp_dl_config.o (.text.SYSCFG_DL_ADC12_0_init)
                  0000246c    00000048     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  000024b4    00000044     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  000024f8    00000042     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_Mcu_Ganv_Sensor_Task_Without_tick)
                  0000253a    00000002     --HOLE-- [fill = 0]
                  0000253c    00000040     driverlib.a : dl_adc12.o (.text.DL_ADC12_setClockConfig)
                  0000257c    00000040     key.o (.text.Key)
                  000025bc    00000040     key.o (.text.Key_1)
                  000025fc    00000040     bsp_usart.o (.text.UART0_IRQHandler)
                  0000263c    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  0000267c    0000003c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  000026b8    0000003c     ti_msp_dl_config.o (.text.DL_Timer_setCounterControl)
                  000026f4    0000003c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Anolog_Value)
                  00002730    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  0000276c    0000003a     libclang_rt.builtins.a : muldsi3.S.obj (.text.__muldsi3)
                  000027a6    00000002     --HOLE-- [fill = 0]
                  000027a8    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  000027dc    00000034     oled.o (.text.OLED_ColorTurn)
                  00002810    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_TIMER_0_init)
                  00002844    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00002878    00000030     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_getMemResult)
                  000028a8    00000030     oled.o (.text.OLED_Pow)
                  000028d8    00000030     systick.o (.text.SysTick_Handler)
                  00002908    0000002c     empty.o (.text.__NVIC_ClearPendingIRQ)
                  00002934    0000002c     empty.o (.text.__NVIC_EnableIRQ)
                  00002960    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  0000298c    00000028     empty.o (.text.DL_Common_updateReg)
                  000029b4    00000028     oled.o (.text.DL_Common_updateReg)
                  000029dc    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  00002a04    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerRXFIFOThreshold)
                  00002a2c    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerTXFIFOThreshold)
                  00002a54    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00002a7c    00000026     ti_msp_dl_config.o (.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
                  00002aa2    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  00002ac8    00000024     libclang_rt.builtins.a : floatunsidf.S.obj (.text.__floatunsidf)
                  00002aec    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  00002b0c    00000020     ti_msp_dl_config.o (.text.DL_SYSCTL_setFlashWaitState)
                  00002b2c    00000020     systick.o (.text.delay_ms)
                  00002b4c    0000001e     ti_msp_dl_config.o (.text.DL_ADC12_setPowerDownMode)
                  00002b6a    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  00002b88    0000001c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_startConversion)
                  00002ba4    0000001c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_stopConversion)
                  00002bc0    0000001c     encoder.o (.text.DL_GPIO_clearInterruptStatus)
                  00002bdc    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  00002bf8    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableHiZ)
                  00002c14    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  00002c30    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  00002c4c    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  00002c68    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  00002c84    0000001c     ti_msp_dl_config.o (.text.DL_Timer_enableInterrupt)
                  00002ca0    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  00002cbc    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  00002cd8    0000001c     ti_msp_dl_config.o (.text.DL_UART_enableInterrupt)
                  00002cf4    00000018     ti_msp_dl_config.o (.text.DL_ADC12_enablePower)
                  00002d0c    00000018     ti_msp_dl_config.o (.text.DL_ADC12_reset)
                  00002d24    00000018     ti_msp_dl_config.o (.text.DL_ADC12_setSampleTime0)
                  00002d3c    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  00002d54    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  00002d6c    00000018     encoder.o (.text.DL_GPIO_getEnabledInterruptStatus)
                  00002d84    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  00002d9c    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  00002db4    00000018     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_GPIO_setPins)
                  00002dcc    00000018     empty.o (.text.DL_GPIO_setPins)
                  00002de4    00000018     motor.o (.text.DL_GPIO_setPins)
                  00002dfc    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setUpperPinsPolarity)
                  00002e14    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableAnalogGlitchFilter)
                  00002e2c    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableController)
                  00002e44    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableControllerClockStretching)
                  00002e5c    00000018     ti_msp_dl_config.o (.text.DL_I2C_enablePower)
                  00002e74    00000018     ti_msp_dl_config.o (.text.DL_I2C_reset)
                  00002e8c    00000018     ti_msp_dl_config.o (.text.DL_I2C_setTimerPeriod)
                  00002ea4    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  00002ebc    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  00002ed4    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  00002eec    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  00002f04    00000018     empty.o (.text.DL_Timer_startCounter)
                  00002f1c    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  00002f34    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  00002f4c    00000016     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_disableConversions)
                  00002f62    00000016     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_enableConversions)
                  00002f78    00000016     ti_msp_dl_config.o (.text.DL_ADC12_enableConversions)
                  00002f8e    00000016     encoder.o (.text.DL_GPIO_readPins)
                  00002fa4    00000016     key.o (.text.DL_GPIO_readPins)
                  00002fba    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  00002fd0    00000014     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_GPIO_clearPins)
                  00002fe4    00000014     empty.o (.text.DL_GPIO_clearPins)
                  00002ff8    00000014     motor.o (.text.DL_GPIO_clearPins)
                  0000300c    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  00003020    00000014     oled.o (.text.DL_I2C_getControllerStatus)
                  00003034    00000014     ti_msp_dl_config.o (.text.DL_I2C_resetControllerTransfer)
                  00003048    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  0000305c    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  00003070    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  00003084    00000014     bsp_usart.o (.text.DL_UART_receiveData)
                  00003098    00000014     motor.o (.text.Left_Control)
                  000030ac    00000014     motor.o (.text.Left_Little_Control)
                  000030c0    00000014     motor.o (.text.Right_Control)
                  000030d4    00000014     motor.o (.text.Right_Little_Control)
                  000030e8    00000012     empty.o (.text.DL_Timer_getPendingInterrupt)
                  000030fa    00000012     bsp_usart.o (.text.DL_UART_getPendingInterrupt)
                  0000310c    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  0000311e    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  00003130    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  00003142    00000010     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_getStatus)
                  00003152    00000002     --HOLE-- [fill = 0]
                  00003154    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  00003164    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  00003174    00000010     libc.a : copy_zero_init.c.obj (.text:decompress:ZI)
                  00003184    0000000e     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Digtal_For_User)
                  00003192    0000000e     libc.a : memset16.S.obj (.text:TI_memset_small)
                  000031a0    0000000c     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  000031ac    0000000c     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memclr)
                  000031b8    0000000c     systick.o (.text.get_systicks)
                  000031c4    0000000c     Scheduler.o (.text.scheduler_init)
                  000031d0    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  000031da    00000002     --HOLE-- [fill = 0]
                  000031dc    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  000031e4    00000006     libc.a : exit.c.obj (.text:abort)
                  000031ea    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  000031ee    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  000031f2    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  000031f6    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)
                  000031fa    00000006     --HOLE-- [fill = 0]

.cinit     0    00004c50    00000058     
                  00004c50    0000002f     (.cinit..data.load) [load image, compression = lzss]
                  00004c7f    00000001     --HOLE-- [fill = 0]
                  00004c80    0000000c     (__TI_handler_table)
                  00004c8c    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00004c94    00000010     (__TI_cinit_table)
                  00004ca4    00000004     --HOLE-- [fill = 0]

.rodata    0    00003200    00001a50     
                  00003200    00000d5c     oled.o (.rodata.asc2_2412)
                  00003f5c    000005f0     oled.o (.rodata.asc2_1608)
                  0000454c    00000474     oled.o (.rodata.asc2_1206)
                  000049c0    00000228     oled.o (.rodata.asc2_0806)
                  00004be8    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  00004c10    00000014     ti_msp_dl_config.o (.rodata.gTIMER_0TimerConfig)
                  00004c24    0000000a     ti_msp_dl_config.o (.rodata.gUART_0Config)
                  00004c2e    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)
                  00004c30    00000008     ti_msp_dl_config.o (.rodata.gADC12_0ClockConfig)
                  00004c38    00000008     ti_msp_dl_config.o (.rodata.gPWM_0Config)
                  00004c40    00000003     ti_msp_dl_config.o (.rodata.gPWM_0ClockConfig)
                  00004c43    00000003     ti_msp_dl_config.o (.rodata.gTIMER_0ClockConfig)
                  00004c46    00000003     empty.o (.rodata.str1.9517790425240694019.1)
                  00004c49    00000002     ti_msp_dl_config.o (.rodata.gUART_0ClockConfig)
                  00004c4b    00000005     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    00000561     UNINITIALIZED
                  20200000    00000480     (.common:OLED_GRAM)
                  20200480    000000bc     (.common:gPWM_0Backup)
                  2020053c    00000004     (.common:Flag_stop)
                  20200540    00000004     (.common:Flag_stop1)
                  20200544    00000004     (.common:Get_Encoder_countA)
                  20200548    00000004     (.common:Get_Encoder_countB)
                  2020054c    00000004     (.common:Run)
                  20200550    00000004     (.common:encoderA_cnt)
                  20200554    00000004     (.common:encoderB_cnt)
                  20200558    00000004     (.common:gpio_interrup1)
                  2020055c    00000004     (.common:gpio_interrup2)
                  20200560    00000001     (.common:task_num)

.data      0    20200568    000001c2     UNINITIALIZED
                  20200568    00000100     empty.o (.data.rx_buff)
                  20200668    00000080     bsp_usart.o (.data.uart_rx_buffer)
                  202006e8    00000010     empty.o (.data.Anolog)
                  202006f8    00000010     empty.o (.data.black)
                  20200708    00000010     empty.o (.data.white)
                  20200718    00000008     systick.o (.data.systicks)
                  20200720    00000004     empty.o (.data.D_Num)
                  20200724    00000004     systick.o (.data.delay_times)
                  20200728    00000001     bsp_usart.o (.data.uart_rx_index)
                  20200729    00000001     bsp_usart.o (.data.uart_rx_ticks)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                           code    ro data   rw data
       ------                           ----    -------   -------
    .\
       ti_msp_dl_config.o               2772    96        188    
       empty.o                          786     3         328    
       startup_mspm0g350x_ticlang.o     8       192       0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           3566    291       516    
                                                                 
    .\app\
       Ganway.o                         1490    0         0      
       No_Mcu_Ganv_Grayscale_Sensor.o   1414    0         0      
       motor.o                          392     0         0      
       encoder.o                        362     0         16     
       key.o                            150     0         0      
       Scheduler.o                      12      0         1      
    +--+--------------------------------+-------+---------+---------+
       Total:                           3820    0         17     
                                                                 
    .\app\OLED\
       oled.o                           2012    6632      1152   
    +--+--------------------------------+-------+---------+---------+
       Total:                           2012    6632      1152   
                                                                 
    .\bsp\
       bsp_usart.o                      102     0         130    
       systick.o                        92      0         12     
    +--+--------------------------------+-------+---------+---------+
       Total:                           194     0         142    
                                                                 
    C:/ti/mspm0_sdk_2_05_01_00/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_timer.o                       588     0         0      
       dl_sysctl_mspm0g1x0x_g3x0x.o     288     0         0      
       dl_i2c.o                         132     0         0      
       dl_uart.o                        90      0         0      
       dl_adc12.o                       64      0         0      
       dl_common.o                      10      0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           1172    0         0      
                                                                 
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       copy_decompress_lzss.c.obj       124     0         0      
       autoinit.c.obj                   60      0         0      
       boot_cortex_m.c.obj              40      0         0      
       copy_decompress_none.c.obj       18      0         0      
       memcpy16.S.obj                   18      0         0      
       copy_zero_init.c.obj             16      0         0      
       memset16.S.obj                   14      0         0      
       exit.c.obj                       6       0         0      
       pre_init.c.obj                   4       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           300     0         0      
                                                                 
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                   4       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           4       0         0      
                                                                 
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                     402     0         0      
       divdf3.S.obj                     268     0         0      
       muldf3.S.obj                     228     0         0      
       comparedf2.c.obj                 220     0         0      
       aeabi_dcmp.S.obj                 98      0         0      
       fixdfsi.S.obj                    74      0         0      
       aeabi_uidivmod.S.obj             64      0         0      
       muldsi3.S.obj                    58      0         0      
       floatsidf.S.obj                  44      0         0      
       floatunsidf.S.obj                36      0         0      
       aeabi_memset.S.obj               12      0         0      
       aeabi_memcpy.S.obj               8       0         0      
       aeabi_div0.c.obj                 2       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           1514    0         0      
                                                                 
       Stack:                           0       0         512    
       Linker Generated:                0       83        0      
    +--+--------------------------------+-------+---------+---------+
       Grand Total:                     12582   7006      2339   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00004c94 records: 2, size/record: 8, table size: 16
	.data: load addr=00004c50, load size=0000002f bytes, run addr=20200568, run size=000001c2 bytes, compression=lzss
	.bss: load addr=00004c8c, load size=00000008 bytes, run addr=20200000, run size=00000561 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00004c80 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
000031eb  ADC0_IRQHandler                      
000031eb  ADC1_IRQHandler                      
000031eb  AES_IRQHandler                       
202006e8  Anolog                               
000031ee  C$$EXIT                              
000031eb  CANFD0_IRQHandler                    
000031eb  DAC0_IRQHandler                      
0000253d  DL_ADC12_setClockConfig              
000031d1  DL_Common_delayCycles                
00002071  DL_I2C_fillControllerTXFIFO          
00002aa3  DL_I2C_setClockConfig                
000015f5  DL_SYSCTL_configSYSPLL               
000024b5  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00001165  DL_Timer_initFourCCPWMMode           
00001269  DL_Timer_initTimerMode               
00002ca1  DL_Timer_setCaptCompUpdateMethod     
00002eed  DL_Timer_setCaptureCompareOutCtl     
00003165  DL_Timer_setCaptureCompareValue      
00002cbd  DL_Timer_setClockConfig              
00002395  DL_UART_init                         
0000310d  DL_UART_setClockConfig               
000031eb  DMA_IRQHandler                       
20200720  D_Num                                
000031eb  Default_Handler                      
2020053c  Flag_stop                            
20200540  Flag_stop1                           
000031eb  GROUP0_IRQHandler                    
00000d15  GROUP1_IRQHandler                    
000017a9  Get_Analog_value                     
000026f5  Get_Anolog_Value                     
00003185  Get_Digtal_For_User                  
20200544  Get_Encoder_countA                   
20200548  Get_Encoder_countB                   
000031ef  HOSTexit                             
000031eb  HardFault_Handler                    
000031eb  I2C0_IRQHandler                      
000031eb  I2C1_IRQHandler                      
0000257d  Key                                  
000025bd  Key_1                                
00003099  Left_Control                         
000030ad  Left_Little_Control                  
000031eb  NMI_Handler                          
00000b8d  No_MCU_Ganv_Sensor_Init              
00001df9  No_MCU_Ganv_Sensor_Init_Frist        
000024f9  No_Mcu_Ganv_Sensor_Task_Without_tick 
00002011  OLED_Clear                           
000027dd  OLED_ColorTurn                       
000023dd  OLED_DisplayTurn                     
00001a59  OLED_DrawPoint                       
20200000  OLED_GRAM                            
00001517  OLED_Init                            
000028a9  OLED_Pow                             
00001c01  OLED_Refresh                         
00000695  OLED_ShowChar                        
00001435  OLED_ShowNum                         
00001923  OLED_ShowSignedNum                   
000019bd  OLED_ShowString                      
00001e6d  OLED_WR_Byte                         
000031eb  PendSV_Handler                       
000031eb  RTC_IRQHandler                       
000031f3  Reset_Handler                        
000030c1  Right_Control                        
000030d5  Right_Little_Control                 
2020054c  Run                                  
000031eb  SPI0_IRQHandler                      
000031eb  SPI1_IRQHandler                      
000031eb  SVC_Handler                          
00002425  SYSCFG_DL_ADC12_0_init               
00000865  SYSCFG_DL_GPIO_init                  
000020d1  SYSCFG_DL_I2C_OLED_init              
00001ae9  SYSCFG_DL_PWM_0_init                 
0000246d  SYSCFG_DL_SYSCTL_init                
000031a1  SYSCFG_DL_SYSTICK_init               
00002811  SYSCFG_DL_TIMER_0_init               
00002129  SYSCFG_DL_UART_0_init                
00002845  SYSCFG_DL_init                       
00001b75  SYSCFG_DL_initPower                  
00000f4d  Set_PWM                              
000028d9  SysTick_Handler                      
000031eb  TIMA0_IRQHandler                     
000031eb  TIMA1_IRQHandler                     
000016d1  TIMG0_IRQHandler                     
000031eb  TIMG12_IRQHandler                    
000031eb  TIMG6_IRQHandler                     
000031eb  TIMG7_IRQHandler                     
000031eb  TIMG8_IRQHandler                     
0000311f  TI_memcpy_small                      
00003193  TI_memset_small                      
000025fd  UART0_IRQHandler                     
000031eb  UART1_IRQHandler                     
000031eb  UART2_IRQHandler                     
000031eb  UART3_IRQHandler                     
000000c1  Way                                  
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00004c94  __TI_CINIT_Base                      
00004ca4  __TI_CINIT_Limit                     
00004ca4  __TI_CINIT_Warm                      
00004c80  __TI_Handler_Table_Base              
00004c8c  __TI_Handler_Table_Limit             
00002731  __TI_auto_init_nobinit_nopinit       
00001d09  __TI_decompress_lzss                 
00003131  __TI_decompress_none                 
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
00003175  __TI_zero_init                       
00000a03  __adddf3                             
00002301  __aeabi_d2iz                         
00000a03  __aeabi_dadd                         
00001fad  __aeabi_dcmpeq                       
00001fe9  __aeabi_dcmpge                       
00001ffd  __aeabi_dcmpgt                       
00001fd5  __aeabi_dcmple                       
00001fc1  __aeabi_dcmplt                       
00001059  __aeabi_ddiv                         
00001351  __aeabi_dmul                         
000009f9  __aeabi_dsub                         
00002961  __aeabi_i2d                          
00000693  __aeabi_idiv0                        
000031ad  __aeabi_memclr                       
000031ad  __aeabi_memclr4                      
000031ad  __aeabi_memclr8                      
000031dd  __aeabi_memcpy                       
000031dd  __aeabi_memcpy4                      
000031dd  __aeabi_memcpy8                      
00002ac9  __aeabi_ui2d                         
0000263d  __aeabi_uidiv                        
0000263d  __aeabi_uidivmod                     
ffffffff  __binit__                            
00001f45  __cmpdf2                             
00001059  __divdf3                             
00001f45  __eqdf2                              
00002301  __fixdfsi                            
00002961  __floatsidf                          
00002ac9  __floatunsidf                        
00001d85  __gedf2                              
00001d85  __gtdf2                              
00001f45  __ledf2                              
00001f45  __ltdf2                              
UNDEFED   __mpu_init                           
00001351  __muldf3                             
0000276d  __muldsi3                            
00001f45  __nedf2                              
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
000009f9  __subdf3                             
00002a55  _c_int00_noargs                      
UNDEFED   _system_post_cinit                   
000031f7  _system_pre_init                     
000031e5  abort                                
0000234b  adc_getValue                         
000049c0  asc2_0806                            
0000454c  asc2_1206                            
00003f5c  asc2_1608                            
00003200  asc2_2412                            
ffffffff  binit                                
202006f8  black                                
00001ed9  convertAnalogToDigital               
00002b2d  delay_ms                             
20200724  delay_times                          
20200550  encoderA_cnt                         
20200554  encoderB_cnt                         
20200480  gPWM_0Backup                         
000031b9  get_systicks                         
20200558  gpio_interrup1                       
2020055c  gpio_interrup2                       
00000000  interruptVectors                     
00000e35  main                                 
00001879  normalizeAnalogValues                
20200568  rx_buff                              
000031c5  scheduler_init                       
20200560  task_num                             
20200668  uart_rx_buffer                       
20200728  uart_rx_index                        
20200729  uart_rx_ticks                        
20200708  white                                


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c1  Way                                  
00000200  __STACK_SIZE                         
00000693  __aeabi_idiv0                        
00000695  OLED_ShowChar                        
00000865  SYSCFG_DL_GPIO_init                  
000009f9  __aeabi_dsub                         
000009f9  __subdf3                             
00000a03  __adddf3                             
00000a03  __aeabi_dadd                         
00000b8d  No_MCU_Ganv_Sensor_Init              
00000d15  GROUP1_IRQHandler                    
00000e35  main                                 
00000f4d  Set_PWM                              
00001059  __aeabi_ddiv                         
00001059  __divdf3                             
00001165  DL_Timer_initFourCCPWMMode           
00001269  DL_Timer_initTimerMode               
00001351  __aeabi_dmul                         
00001351  __muldf3                             
00001435  OLED_ShowNum                         
00001517  OLED_Init                            
000015f5  DL_SYSCTL_configSYSPLL               
000016d1  TIMG0_IRQHandler                     
000017a9  Get_Analog_value                     
00001879  normalizeAnalogValues                
00001923  OLED_ShowSignedNum                   
000019bd  OLED_ShowString                      
00001a59  OLED_DrawPoint                       
00001ae9  SYSCFG_DL_PWM_0_init                 
00001b75  SYSCFG_DL_initPower                  
00001c01  OLED_Refresh                         
00001d09  __TI_decompress_lzss                 
00001d85  __gedf2                              
00001d85  __gtdf2                              
00001df9  No_MCU_Ganv_Sensor_Init_Frist        
00001e6d  OLED_WR_Byte                         
00001ed9  convertAnalogToDigital               
00001f45  __cmpdf2                             
00001f45  __eqdf2                              
00001f45  __ledf2                              
00001f45  __ltdf2                              
00001f45  __nedf2                              
00001fad  __aeabi_dcmpeq                       
00001fc1  __aeabi_dcmplt                       
00001fd5  __aeabi_dcmple                       
00001fe9  __aeabi_dcmpge                       
00001ffd  __aeabi_dcmpgt                       
00002011  OLED_Clear                           
00002071  DL_I2C_fillControllerTXFIFO          
000020d1  SYSCFG_DL_I2C_OLED_init              
00002129  SYSCFG_DL_UART_0_init                
00002301  __aeabi_d2iz                         
00002301  __fixdfsi                            
0000234b  adc_getValue                         
00002395  DL_UART_init                         
000023dd  OLED_DisplayTurn                     
00002425  SYSCFG_DL_ADC12_0_init               
0000246d  SYSCFG_DL_SYSCTL_init                
000024b5  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
000024f9  No_Mcu_Ganv_Sensor_Task_Without_tick 
0000253d  DL_ADC12_setClockConfig              
0000257d  Key                                  
000025bd  Key_1                                
000025fd  UART0_IRQHandler                     
0000263d  __aeabi_uidiv                        
0000263d  __aeabi_uidivmod                     
000026f5  Get_Anolog_Value                     
00002731  __TI_auto_init_nobinit_nopinit       
0000276d  __muldsi3                            
000027dd  OLED_ColorTurn                       
00002811  SYSCFG_DL_TIMER_0_init               
00002845  SYSCFG_DL_init                       
000028a9  OLED_Pow                             
000028d9  SysTick_Handler                      
00002961  __aeabi_i2d                          
00002961  __floatsidf                          
00002a55  _c_int00_noargs                      
00002aa3  DL_I2C_setClockConfig                
00002ac9  __aeabi_ui2d                         
00002ac9  __floatunsidf                        
00002b2d  delay_ms                             
00002ca1  DL_Timer_setCaptCompUpdateMethod     
00002cbd  DL_Timer_setClockConfig              
00002eed  DL_Timer_setCaptureCompareOutCtl     
00003099  Left_Control                         
000030ad  Left_Little_Control                  
000030c1  Right_Control                        
000030d5  Right_Little_Control                 
0000310d  DL_UART_setClockConfig               
0000311f  TI_memcpy_small                      
00003131  __TI_decompress_none                 
00003165  DL_Timer_setCaptureCompareValue      
00003175  __TI_zero_init                       
00003185  Get_Digtal_For_User                  
00003193  TI_memset_small                      
000031a1  SYSCFG_DL_SYSTICK_init               
000031ad  __aeabi_memclr                       
000031ad  __aeabi_memclr4                      
000031ad  __aeabi_memclr8                      
000031b9  get_systicks                         
000031c5  scheduler_init                       
000031d1  DL_Common_delayCycles                
000031dd  __aeabi_memcpy                       
000031dd  __aeabi_memcpy4                      
000031dd  __aeabi_memcpy8                      
000031e5  abort                                
000031eb  ADC0_IRQHandler                      
000031eb  ADC1_IRQHandler                      
000031eb  AES_IRQHandler                       
000031eb  CANFD0_IRQHandler                    
000031eb  DAC0_IRQHandler                      
000031eb  DMA_IRQHandler                       
000031eb  Default_Handler                      
000031eb  GROUP0_IRQHandler                    
000031eb  HardFault_Handler                    
000031eb  I2C0_IRQHandler                      
000031eb  I2C1_IRQHandler                      
000031eb  NMI_Handler                          
000031eb  PendSV_Handler                       
000031eb  RTC_IRQHandler                       
000031eb  SPI0_IRQHandler                      
000031eb  SPI1_IRQHandler                      
000031eb  SVC_Handler                          
000031eb  TIMA0_IRQHandler                     
000031eb  TIMA1_IRQHandler                     
000031eb  TIMG12_IRQHandler                    
000031eb  TIMG6_IRQHandler                     
000031eb  TIMG7_IRQHandler                     
000031eb  TIMG8_IRQHandler                     
000031eb  UART1_IRQHandler                     
000031eb  UART2_IRQHandler                     
000031eb  UART3_IRQHandler                     
000031ee  C$$EXIT                              
000031ef  HOSTexit                             
000031f3  Reset_Handler                        
000031f7  _system_pre_init                     
00003200  asc2_2412                            
00003f5c  asc2_1608                            
0000454c  asc2_1206                            
000049c0  asc2_0806                            
00004c80  __TI_Handler_Table_Base              
00004c8c  __TI_Handler_Table_Limit             
00004c94  __TI_CINIT_Base                      
00004ca4  __TI_CINIT_Limit                     
00004ca4  __TI_CINIT_Warm                      
20200000  OLED_GRAM                            
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
20200480  gPWM_0Backup                         
2020053c  Flag_stop                            
20200540  Flag_stop1                           
20200544  Get_Encoder_countA                   
20200548  Get_Encoder_countB                   
2020054c  Run                                  
20200550  encoderA_cnt                         
20200554  encoderB_cnt                         
20200558  gpio_interrup1                       
2020055c  gpio_interrup2                       
20200560  task_num                             
20200568  rx_buff                              
20200668  uart_rx_buffer                       
202006e8  Anolog                               
202006f8  black                                
20200708  white                                
20200720  D_Num                                
20200724  delay_times                          
20200728  uart_rx_index                        
20200729  uart_rx_ticks                        
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[190 symbols]
